import { Link } from 'react-router-dom';
import { useState } from 'react';

// Import local images
import forestImg from '../../assets/photos/forest.webp';

const HeroSection = () => {
  const [activeTab, setActiveTab] = useState('packages');

  return (
    <div className="relative">
      {/* Main Hero Section */}
      <div className="h-[70vh] relative overflow-hidden bg-gradient-to-br from-orange-500 via-red-500 to-red-600">
        {/* Background Image */}
        <div className="absolute inset-0">
          <img
            src={forestImg}
            alt="North East India"
            className="w-full h-full object-cover opacity-40"
            loading="eager"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-orange-900/60 to-red-800/40"></div>
        </div>

        {/* Content */}
        <div className="absolute inset-0 z-10 flex flex-col justify-center">
          <div className="max-w-6xl mx-auto px-4 w-full">
            {/* Header Text */}
            <div className="text-center mb-8">
              <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
                Explore North East India
              </h1>
              <p className="text-xl text-orange-100 max-w-2xl mx-auto">
                Discover amazing destinations with our curated travel packages
              </p>
            </div>

            {/* Search Widget */}
            <div className="bg-white rounded-2xl shadow-2xl p-6 max-w-5xl mx-auto">
              {/* Tabs */}
              <div className="flex flex-wrap gap-2 mb-6 border-b border-gray-200">
                <button
                  onClick={() => setActiveTab('packages')}
                  className={`px-6 py-3 font-medium rounded-t-lg transition-all duration-200 ${
                    activeTab === 'packages'
                      ? 'bg-orange-600 text-white border-b-2 border-orange-600'
                      : 'text-gray-600 hover:text-orange-600 hover:bg-orange-50'
                  }`}
                >
                  <i className="fas fa-map-marked-alt mr-2"></i>
                  Tour Packages
                </button>
                <button
                  onClick={() => setActiveTab('hotels')}
                  className={`px-6 py-3 font-medium rounded-t-lg transition-all duration-200 ${
                    activeTab === 'hotels'
                      ? 'bg-orange-600 text-white border-b-2 border-orange-600'
                      : 'text-gray-600 hover:text-orange-600 hover:bg-orange-50'
                  }`}
                >
                  <i className="fas fa-bed mr-2"></i>
                  Hotels
                </button>
                <button
                  onClick={() => setActiveTab('transport')}
                  className={`px-6 py-3 font-medium rounded-t-lg transition-all duration-200 ${
                    activeTab === 'transport'
                      ? 'bg-orange-600 text-white border-b-2 border-orange-600'
                      : 'text-gray-600 hover:text-orange-600 hover:bg-orange-50'
                  }`}
                >
                  <i className="fas fa-car mr-2"></i>
                  Transport
                </button>
              </div>

              {/* Search Form */}
              {activeTab === 'packages' && (
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <i className="fas fa-map-marker-alt mr-1 text-orange-600"></i>
                      Destination
                    </label>
                    <select className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                      <option>Select Destination</option>
                      <option>Darjeeling</option>
                      <option>Gangtok</option>
                      <option>North Sikkim</option>
                      <option>Pelling</option>
                      <option>Dooars</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <i className="fas fa-calendar mr-1 text-orange-600"></i>
                      Travel Date
                    </label>
                    <input
                      type="date"
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <i className="fas fa-users mr-1 text-orange-600"></i>
                      Travelers
                    </label>
                    <select className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                      <option>1 Person</option>
                      <option>2 People</option>
                      <option>3-5 People</option>
                      <option>6+ People</option>
                    </select>
                  </div>
                </div>
              )}

              {activeTab === 'hotels' && (
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <i className="fas fa-map-marker-alt mr-1 text-orange-600"></i>
                      City
                    </label>
                    <input
                      type="text"
                      placeholder="Enter city name"
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <i className="fas fa-calendar mr-1 text-orange-600"></i>
                      Check-in
                    </label>
                    <input
                      type="date"
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <i className="fas fa-calendar mr-1 text-orange-600"></i>
                      Check-out
                    </label>
                    <input
                      type="date"
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <i className="fas fa-users mr-1 text-orange-600"></i>
                      Guests
                    </label>
                    <select className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                      <option>1 Guest</option>
                      <option>2 Guests</option>
                      <option>3 Guests</option>
                      <option>4+ Guests</option>
                    </select>
                  </div>
                </div>
              )}

              {activeTab === 'transport' && (
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <i className="fas fa-map-marker-alt mr-1 text-blue-600"></i>
                      From
                    </label>
                    <input
                      type="text"
                      placeholder="Pickup location"
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <i className="fas fa-map-marker-alt mr-1 text-blue-600"></i>
                      To
                    </label>
                    <input
                      type="text"
                      placeholder="Drop location"
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <i className="fas fa-calendar mr-1 text-blue-600"></i>
                      Date
                    </label>
                    <input
                      type="date"
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      <i className="fas fa-car mr-1 text-blue-600"></i>
                      Vehicle
                    </label>
                    <select className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                      <option>Sedan</option>
                      <option>SUV</option>
                      <option>Tempo Traveller</option>
                      <option>Bus</option>
                    </select>
                  </div>
                </div>
              )}

              {/* Search Button */}
              <div className="mt-6 text-center">
                <Link
                  to="/plan"
                  className="inline-flex items-center bg-gradient-to-r from-blue-600 to-blue-700 text-white px-12 py-4 font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 hover:from-blue-700 hover:to-blue-800"
                >
                  <i className="fas fa-search mr-2"></i>
                  Search Now
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Popular Destinations Bar */}
      <div className="bg-white py-4 shadow-md relative z-10 border-t border-gray-100">
        <div className="max-w-6xl mx-auto px-4">
          <div className="flex justify-center items-center flex-wrap gap-6">
            <span className="text-gray-600 font-medium">Popular:</span>
            <Link
              to="/plan?destination=darjeeling"
              className="flex items-center text-gray-700 hover:text-blue-600 font-medium transition-all duration-200 hover:scale-105"
            >
              <i className="fas fa-mountain text-blue-500 mr-2"></i>
              Darjeeling
            </Link>
            <Link
              to="/plan?destination=gangtok"
              className="flex items-center text-gray-700 hover:text-blue-600 font-medium transition-all duration-200 hover:scale-105"
            >
              <i className="fas fa-city text-blue-500 mr-2"></i>
              Gangtok
            </Link>
            <Link
              to="/plan?destination=sikkim"
              className="flex items-center text-gray-700 hover:text-blue-600 font-medium transition-all duration-200 hover:scale-105"
            >
              <i className="fas fa-snowflake text-blue-500 mr-2"></i>
              North Sikkim
            </Link>
            <Link
              to="/plan?destination=pelling"
              className="flex items-center text-gray-700 hover:text-blue-600 font-medium transition-all duration-200 hover:scale-105"
            >
              <i className="fas fa-tree text-blue-500 mr-2"></i>
              Pelling
            </Link>
            <Link
              to="/plan"
              className="flex items-center text-blue-600 hover:text-blue-700 font-semibold transition-all duration-200"
            >
              View All
              <i className="fas fa-arrow-right text-xs ml-1"></i>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;
