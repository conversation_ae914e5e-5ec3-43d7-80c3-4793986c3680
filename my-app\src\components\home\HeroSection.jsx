import { Link } from 'react-router-dom';

// Import local images
import forestImg from '../../assets/photos/forest.webp';

const HeroSection = () => {

  return (
    <div className="relative">
      {/* Main Hero Section */}
      <div className="h-[65vh] relative overflow-hidden bg-gradient-to-br from-emerald-50 via-teal-50 to-cyan-50">
        {/* Background Image */}
        <div className="absolute inset-0">
          <img
            src={forestImg}
            alt="North East India Forest"
            className="w-full h-full object-cover opacity-60"
            loading="eager"
          />
          <div className="absolute inset-0 bg-gradient-to-br from-emerald-900/20 via-teal-800/30 to-cyan-900/20"></div>
        </div>

        {/* Decorative Shapes */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden">
          <div className="absolute -top-20 -left-20 w-40 h-40 bg-emerald-200/30 rounded-full blur-3xl"></div>
          <div className="absolute top-20 right-10 w-32 h-32 bg-teal-300/20 rounded-full blur-2xl"></div>
          <div className="absolute bottom-10 left-1/4 w-24 h-24 bg-cyan-200/25 rounded-full blur-xl"></div>
        </div>

        {/* Content */}
        <div className="absolute inset-0 z-10 flex items-center">
          <div className="max-w-7xl mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              {/* Left Content */}
              <div className="text-left">
                <div className="inline-flex items-center px-4 py-2 bg-emerald-100/80 backdrop-blur-sm border border-emerald-200/50 rounded-full text-emerald-800 text-sm font-medium mb-6">
                  <i className="fas fa-leaf mr-2 text-emerald-600"></i>
                  Explore North East India
                </div>

                <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6">
                  Journey to the
                  <span className="block text-transparent bg-clip-text bg-gradient-to-r from-emerald-600 via-teal-600 to-cyan-600">
                    Untouched Wilderness
                  </span>
                </h1>

                <p className="text-lg md:text-xl text-gray-700 mb-8 max-w-xl leading-relaxed">
                  Discover pristine landscapes, vibrant cultures, and unforgettable adventures in India's most beautiful northeastern states.
                </p>

                <div className="flex flex-col sm:flex-row gap-4">
                  <Link
                    to="/plan"
                    className="bg-gradient-to-r from-emerald-600 to-teal-600 text-white px-8 py-4 font-semibold rounded-xl shadow-lg transition-all duration-300 hover:shadow-xl hover:scale-105 hover:from-emerald-700 hover:to-teal-700"
                  >
                    <i className="fas fa-compass mr-2"></i>
                    Start Planning
                  </Link>
                  <Link
                    to="/gallery"
                    className="bg-white/90 backdrop-blur-sm border-2 border-emerald-200 text-emerald-700 px-8 py-4 font-semibold rounded-xl transition-all duration-300 hover:bg-emerald-50 hover:border-emerald-300 hover:shadow-lg"
                  >
                    <i className="fas fa-camera mr-2"></i>
                    View Photos
                  </Link>
                </div>
              </div>

              {/* Right Content - Stats/Features */}
              <div className="hidden lg:block">
                <div className="grid grid-cols-2 gap-6">
                  <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/50">
                    <div className="text-3xl font-bold text-emerald-600 mb-2">50+</div>
                    <div className="text-gray-700 font-medium">Destinations</div>
                  </div>
                  <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/50">
                    <div className="text-3xl font-bold text-teal-600 mb-2">1000+</div>
                    <div className="text-gray-700 font-medium">Happy Travelers</div>
                  </div>
                  <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/50">
                    <div className="text-3xl font-bold text-cyan-600 mb-2">15+</div>
                    <div className="text-gray-700 font-medium">Years Experience</div>
                  </div>
                  <div className="bg-white/80 backdrop-blur-sm p-6 rounded-2xl shadow-lg border border-white/50">
                    <div className="text-3xl font-bold text-emerald-600 mb-2">24/7</div>
                    <div className="text-gray-700 font-medium">Support</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Destination Links */}
      <div className="bg-gradient-to-r from-emerald-50 to-teal-50 py-8 shadow-lg relative z-10 border-t border-emerald-100">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">Popular Destinations</h3>
            <div className="w-16 h-1 bg-gradient-to-r from-emerald-500 to-teal-500 mx-auto rounded-full"></div>
          </div>
          <div className="flex justify-center flex-wrap gap-8">
            <Link
              to="/plan?destination=darjeeling"
              className="group flex items-center text-gray-700 hover:text-emerald-600 font-medium transition-all duration-300"
            >
              <div className="w-12 h-12 rounded-full bg-gradient-to-r from-emerald-100 to-teal-100 flex items-center justify-center mr-3 group-hover:from-emerald-500 group-hover:to-teal-500 transition-all duration-300 shadow-md">
                <i className="fas fa-mountain text-emerald-600 group-hover:text-white text-sm transition-colors duration-300"></i>
              </div>
              <span className="group-hover:scale-105 transition-transform duration-300">Darjeeling</span>
            </Link>

            <Link
              to="/plan?destination=gangtok"
              className="group flex items-center text-gray-700 hover:text-emerald-600 font-medium transition-all duration-300"
            >
              <div className="w-12 h-12 rounded-full bg-gradient-to-r from-emerald-100 to-teal-100 flex items-center justify-center mr-3 group-hover:from-emerald-500 group-hover:to-teal-500 transition-all duration-300 shadow-md">
                <i className="fas fa-city text-emerald-600 group-hover:text-white text-sm transition-colors duration-300"></i>
              </div>
              <span className="group-hover:scale-105 transition-transform duration-300">Gangtok</span>
            </Link>

            <Link
              to="/plan?destination=sikkim"
              className="group flex items-center text-gray-700 hover:text-emerald-600 font-medium transition-all duration-300"
            >
              <div className="w-12 h-12 rounded-full bg-gradient-to-r from-emerald-100 to-teal-100 flex items-center justify-center mr-3 group-hover:from-emerald-500 group-hover:to-teal-500 transition-all duration-300 shadow-md">
                <i className="fas fa-snowflake text-emerald-600 group-hover:text-white text-sm transition-colors duration-300"></i>
              </div>
              <span className="group-hover:scale-105 transition-transform duration-300">North Sikkim</span>
            </Link>

            <Link
              to="/plan?destination=pelling"
              className="group flex items-center text-gray-700 hover:text-emerald-600 font-medium transition-all duration-300"
            >
              <div className="w-12 h-12 rounded-full bg-gradient-to-r from-emerald-100 to-teal-100 flex items-center justify-center mr-3 group-hover:from-emerald-500 group-hover:to-teal-500 transition-all duration-300 shadow-md">
                <i className="fas fa-tree text-emerald-600 group-hover:text-white text-sm transition-colors duration-300"></i>
              </div>
              <span className="group-hover:scale-105 transition-transform duration-300">Pelling</span>
            </Link>

            <Link
              to="/plan"
              className="group flex items-center text-emerald-600 hover:text-emerald-700 font-semibold transition-all duration-300 bg-white/60 backdrop-blur-sm px-4 py-2 rounded-full border border-emerald-200 hover:bg-white/80 hover:shadow-md"
            >
              <span className="group-hover:scale-105 transition-transform duration-300">View All</span>
              <i className="fas fa-arrow-right text-xs ml-2 group-hover:translate-x-1 transition-transform duration-300"></i>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;
