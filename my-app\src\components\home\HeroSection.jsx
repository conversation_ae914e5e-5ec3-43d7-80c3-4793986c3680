import { Link } from 'react-router-dom';

// Import local images
import forestImg from '../../assets/photos/forest.webp';

const HeroSection = () => {

  return (
    <div className="relative">
      {/* Main Hero Section */}
      <div className="h-[60vh] relative overflow-hidden bg-gradient-to-br from-indigo-900 via-indigo-800 to-blue-900">
        {/* Background Image */}
        <div className="absolute inset-0">
          <img
            src={forestImg}
            alt="North East India Forest"
            className="w-full h-full object-cover opacity-30"
            loading="eager"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-indigo-900/80 via-indigo-800/60 to-blue-900/80"></div>
        </div>

        {/* Content */}
        <div className="absolute inset-0 z-10 flex items-center">
          <div className="max-w-6xl mx-auto px-4 text-center">
            <div className="max-w-4xl mx-auto">
              <div className="inline-block px-4 py-2 bg-white/10 backdrop-blur-sm border border-white/20 rounded-full text-white text-sm font-medium mb-6">
                <i className="fas fa-mountain mr-2"></i>
                Gateway to North East India
              </div>

              <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-white leading-tight mb-6">
                Discover the
                <span className="block text-transparent bg-clip-text bg-gradient-to-r from-blue-200 to-indigo-200">
                  Hidden Paradise
                </span>
              </h1>

              <p className="text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto leading-relaxed">
                Experience the untouched beauty of North East India with our expertly crafted travel packages
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <Link
                  to="/plan"
                  className="bg-gradient-to-r from-indigo-500 to-blue-600 text-white px-8 py-4 font-semibold rounded-lg transition-all duration-300 hover:shadow-xl hover:scale-105 hover:from-indigo-600 hover:to-blue-700"
                >
                  <i className="fas fa-route mr-2"></i>
                  Plan Your Journey
                </Link>
                <Link
                  to="/gallery"
                  className="bg-white/10 backdrop-blur-sm border border-white/30 text-white px-8 py-4 font-semibold rounded-lg transition-all duration-300 hover:bg-white/20 hover:shadow-lg"
                >
                  <i className="fas fa-images mr-2"></i>
                  Explore Gallery
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Floating Elements */}
        <div className="absolute top-20 left-10 w-2 h-2 bg-blue-300 rounded-full opacity-60 animate-pulse"></div>
        <div className="absolute top-32 right-20 w-3 h-3 bg-indigo-300 rounded-full opacity-40 animate-pulse" style={{animationDelay: '1s'}}></div>
        <div className="absolute bottom-20 left-20 w-1 h-1 bg-white rounded-full opacity-50 animate-pulse" style={{animationDelay: '2s'}}></div>
        <div className="absolute bottom-32 right-10 w-2 h-2 bg-blue-200 rounded-full opacity-30 animate-pulse" style={{animationDelay: '0.5s'}}></div>
      </div>

      {/* Quick Destination Links */}
      <div className="bg-white py-6 shadow-lg relative z-10 border-t border-gray-100">
        <div className="max-w-6xl mx-auto px-4">
          <div className="flex justify-center flex-wrap gap-6">
            <Link
              to="/plan?destination=darjeeling"
              className="group flex items-center text-gray-700 hover:text-indigo-600 font-medium transition-all duration-300"
            >
              <div className="w-10 h-10 rounded-full bg-gradient-to-r from-indigo-100 to-blue-100 flex items-center justify-center mr-3 group-hover:from-indigo-500 group-hover:to-blue-500 transition-all duration-300">
                <i className="fas fa-mountain text-indigo-500 group-hover:text-white text-sm transition-colors duration-300"></i>
              </div>
              <span className="group-hover:scale-105 transition-transform duration-300">Darjeeling</span>
            </Link>

            <Link
              to="/plan?destination=gangtok"
              className="group flex items-center text-gray-700 hover:text-indigo-600 font-medium transition-all duration-300"
            >
              <div className="w-10 h-10 rounded-full bg-gradient-to-r from-indigo-100 to-blue-100 flex items-center justify-center mr-3 group-hover:from-indigo-500 group-hover:to-blue-500 transition-all duration-300">
                <i className="fas fa-city text-indigo-500 group-hover:text-white text-sm transition-colors duration-300"></i>
              </div>
              <span className="group-hover:scale-105 transition-transform duration-300">Gangtok</span>
            </Link>

            <Link
              to="/plan?destination=sikkim"
              className="group flex items-center text-gray-700 hover:text-indigo-600 font-medium transition-all duration-300"
            >
              <div className="w-10 h-10 rounded-full bg-gradient-to-r from-indigo-100 to-blue-100 flex items-center justify-center mr-3 group-hover:from-indigo-500 group-hover:to-blue-500 transition-all duration-300">
                <i className="fas fa-snowflake text-indigo-500 group-hover:text-white text-sm transition-colors duration-300"></i>
              </div>
              <span className="group-hover:scale-105 transition-transform duration-300">North Sikkim</span>
            </Link>

            <Link
              to="/plan?destination=pelling"
              className="group flex items-center text-gray-700 hover:text-indigo-600 font-medium transition-all duration-300"
            >
              <div className="w-10 h-10 rounded-full bg-gradient-to-r from-indigo-100 to-blue-100 flex items-center justify-center mr-3 group-hover:from-indigo-500 group-hover:to-blue-500 transition-all duration-300">
                <i className="fas fa-tree text-indigo-500 group-hover:text-white text-sm transition-colors duration-300"></i>
              </div>
              <span className="group-hover:scale-105 transition-transform duration-300">Pelling</span>
            </Link>

            <Link
              to="/plan"
              className="group flex items-center text-indigo-600 hover:text-indigo-700 font-semibold transition-all duration-300"
            >
              <span className="group-hover:scale-105 transition-transform duration-300">View All Destinations</span>
              <i className="fas fa-arrow-right text-xs ml-2 group-hover:translate-x-1 transition-transform duration-300"></i>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;
