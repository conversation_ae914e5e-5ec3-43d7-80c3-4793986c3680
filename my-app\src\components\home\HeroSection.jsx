import { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';

// Import local images
import darjeelingImg from '../../assets/photos/dargeeling2.webp';
import gangtokImg from '../../assets/photos/city_gangtok.webp';
import panoramaImg from '../../assets/photos/paranoma1.webp';
import natureImg from '../../assets/photos/nature4.webp';

const HeroSection = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [direction, setDirection] = useState('next');
  const slideTimerRef = useRef(null);

  const slides = [
    {
      image: darjeelingImg,
      title: "Darjeeling",
      tagline: "Queen of Hills",
      description: "Discover breathtaking landscapes and world-famous tea gardens",
      color: "from-yellow-500 to-yellow-600"
    },
    {
      image: gangtokImg,
      title: "Gangtok",
      tagline: "Capital of Sikkim",
      description: "Immerse yourself in vibrant culture and stunning Himalayan views",
      color: "from-blue-500 to-blue-600"
    },
    {
      image: panoramaImg,
      title: "North Sikkim",
      tagline: "Pristine Wilderness",
      description: "Journey through the untouched landscapes of Lachen and Lachung",
      color: "from-green-500 to-green-600"
    },
    {
      image: natureImg,
      title: "Natural Wonders",
      tagline: "Diverse Ecosystems",
      description: "Explore the untouched beauty of North East India's diverse ecosystems",
      color: "from-purple-500 to-purple-600"
    }
  ];

  const resetSlideTimer = () => {
    if (slideTimerRef.current) {
      clearTimeout(slideTimerRef.current);
    }

    slideTimerRef.current = setTimeout(() => {
      changeSlide('next');
    }, 6000);
  };

  useEffect(() => {
    resetSlideTimer();
    return () => {
      if (slideTimerRef.current) {
        clearTimeout(slideTimerRef.current);
      }
    };
  }, [currentSlide]);

  const changeSlide = (dir) => {
    if (isAnimating) return;

    setIsAnimating(true);
    setDirection(dir);

    let nextIndex;
    if (dir === 'next') {
      nextIndex = (currentSlide + 1) % slides.length;
    } else {
      nextIndex = (currentSlide - 1 + slides.length) % slides.length;
    }

    setCurrentSlide(nextIndex);
    setTimeout(() => setIsAnimating(false), 1000);
  };

  const goToSlide = (index) => {
    if (isAnimating || index === currentSlide) return;

    setDirection(index > currentSlide ? 'next' : 'prev');
    setIsAnimating(true);
    setCurrentSlide(index);
    setTimeout(() => setIsAnimating(false), 1000);
  };

  return (
    <div className="relative">
      {/* Main Hero Section */}
      <div className="h-[80vh] relative overflow-hidden bg-gray-900">
        {/* Background Images with Ken Burns effect */}
        {slides.map((slide, index) => (
          <div
            key={index}
            className={`absolute inset-0 transition-all duration-1500 ease-in-out ${
              index === currentSlide
                ? 'opacity-100 z-10 scale-105 animate-kenBurns'
                : 'opacity-0 z-0'
            }`}
          >
            <img
              src={slide.image}
              alt={slide.title}
              className="w-full h-full object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/40 to-transparent"></div>
          </div>
        ))}

        {/* Animated Content */}
        <div className="absolute inset-0 z-20 flex items-center">
          <div className="container mx-auto px-4 md:px-8 lg:px-16">
            <div className="relative overflow-hidden h-[400px]">
              {slides.map((slide, index) => (
                <div
                  key={index}
                  className={`absolute inset-0 transition-all duration-1000 ease-in-out flex flex-col justify-center ${
                    index === currentSlide
                      ? direction === 'next'
                        ? 'translate-x-0 opacity-100'
                        : 'translate-x-0 opacity-100'
                      : direction === 'next'
                        ? index === (currentSlide - 1 + slides.length) % slides.length
                          ? '-translate-x-full opacity-0'
                          : 'translate-x-full opacity-0'
                        : index === (currentSlide + 1) % slides.length
                          ? 'translate-x-full opacity-0'
                          : '-translate-x-full opacity-0'
                  }`}
                >
                  <div className="max-w-xl">
                    <div
                      className={`inline-block px-4 py-1 rounded-full bg-gradient-to-r ${slide.color} text-white text-sm font-medium mb-4 animate-fadeInUp opacity-0`}
                      style={{animationDelay: '0.3s', animationFillMode: 'forwards'}}
                    >
                      {slide.tagline}
                    </div>
                    <h1
                      className="text-5xl md:text-7xl font-bold text-white leading-tight mb-4 animate-fadeInUp opacity-0"
                      style={{animationDelay: '0.5s', animationFillMode: 'forwards'}}
                    >
                      {slide.title}
                    </h1>
                    <p
                      className="text-xl text-white/90 mb-8 animate-fadeInUp opacity-0"
                      style={{animationDelay: '0.7s', animationFillMode: 'forwards'}}
                    >
                      {slide.description}
                    </p>
                    <div
                      className="flex flex-wrap gap-4 animate-fadeInUp opacity-0"
                      style={{animationDelay: '0.9s', animationFillMode: 'forwards'}}
                    >
                      <Link
                        to="/plan"
                        className={`bg-gradient-to-r ${slide.color} text-white px-8 py-3 font-medium transition-all duration-300 hover:shadow-lg hover:scale-105`}
                      >
                        Plan Your Trip
                      </Link>
                      <Link
                        to="/gallery"
                        className="bg-white/10 backdrop-blur-sm border border-white/30 text-white px-8 py-3 font-medium transition-all duration-300 hover:bg-white/20 hover:shadow-lg"
                      >
                        View Gallery
                      </Link>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Navigation Arrows */}
        <div className="absolute inset-y-0 left-0 z-20 flex items-center">
          <button
            onClick={() => changeSlide('prev')}
            className="bg-black/30 hover:bg-black/50 text-white w-12 h-12 flex items-center justify-center backdrop-blur-sm transition-all duration-300 hover:scale-110"
            aria-label="Previous slide"
          >
            <i className="fas fa-chevron-left"></i>
          </button>
        </div>
        <div className="absolute inset-y-0 right-0 z-20 flex items-center">
          <button
            onClick={() => changeSlide('next')}
            className="bg-black/30 hover:bg-black/50 text-white w-12 h-12 flex items-center justify-center backdrop-blur-sm transition-all duration-300 hover:scale-110"
            aria-label="Next slide"
          >
            <i className="fas fa-chevron-right"></i>
          </button>
        </div>

        {/* Slide Indicators */}
        <div className="absolute bottom-8 left-0 right-0 z-20 flex justify-center space-x-2">
          {slides.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`transition-all duration-500 ${
                index === currentSlide
                  ? `w-8 h-2 bg-gradient-to-r ${slides[currentSlide].color}`
                  : 'w-2 h-2 bg-white/50 hover:bg-white/70'
              } rounded-full`}
              aria-label={`Go to slide ${index + 1}`}
            ></button>
          ))}
        </div>
      </div>

      {/* Animated Destination Quick Links */}
      <div className="bg-white py-4 shadow-md relative z-10">
        <div className="container mx-auto px-4">
          <div className="flex justify-center flex-wrap gap-x-8 gap-y-3">
            <Link
              to="/plan?destination=darjeeling"
              className="flex items-center text-gray-800 hover:text-yellow-500 font-medium transition-all duration-300 hover:scale-105"
            >
              <div className="w-8 h-8 rounded-full bg-yellow-100 flex items-center justify-center mr-2">
                <i className="fas fa-mountain text-yellow-500 text-sm"></i>
              </div>
              <span>Darjeeling</span>
            </Link>
            <Link
              to="/plan?destination=gangtok"
              className="flex items-center text-gray-800 hover:text-blue-500 font-medium transition-all duration-300 hover:scale-105"
            >
              <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-2">
                <i className="fas fa-city text-blue-500 text-sm"></i>
              </div>
              <span>Gangtok</span>
            </Link>
            <Link
              to="/plan?destination=sikkim"
              className="flex items-center text-gray-800 hover:text-green-500 font-medium transition-all duration-300 hover:scale-105"
            >
              <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-2">
                <i className="fas fa-snowflake text-green-500 text-sm"></i>
              </div>
              <span>North Sikkim</span>
            </Link>
            <Link
              to="/plan?destination=pelling"
              className="flex items-center text-gray-800 hover:text-purple-500 font-medium transition-all duration-300 hover:scale-105"
            >
              <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-2">
                <i className="fas fa-tree text-purple-500 text-sm"></i>
              </div>
              <span>Pelling</span>
            </Link>
            <Link
              to="/plan"
              className="flex items-center text-yellow-500 hover:text-yellow-600 font-medium transition-all duration-300 hover:scale-105"
            >
              <span>View All</span>
              <i className="fas fa-arrow-right text-xs ml-2 animate-bounceX"></i>
            </Link>
          </div>
        </div>
      </div>

      {/* Add CSS animations */}
      <style>{`
        @keyframes kenBurns {
          0% {
            transform: scale(1);
          }
          100% {
            transform: scale(1.05);
          }
        }

        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes bounceX {
          0%, 100% {
            transform: translateX(0);
          }
          50% {
            transform: translateX(3px);
          }
        }

        .animate-kenBurns {
          animation: kenBurns 6s ease-in-out forwards;
        }

        .animate-fadeInUp {
          animation: fadeInUp 0.8s ease-out forwards;
        }

        .animate-bounceX {
          animation: bounceX 1s ease-in-out infinite;
        }

        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }

        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
      `}</style>
    </div>
  );
};

export default HeroSection;
